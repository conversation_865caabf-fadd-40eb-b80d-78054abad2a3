["\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\menu\\autogenerated\\default.toml"]