cargo:rerun-if-env-changed=LIBSQLITE3_SYS_USE_PKG_CONFIG
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1/sqlite3
cargo:rerun-if-changed=sqlite3/sqlite3.c
cargo:rerun-if-changed=sqlite3/wasm32-wasi-vfs.c
cargo:rerun-if-env-changed=SQLITE_MAX_VARIABLE_NUMBER
cargo:rerun-if-env-changed=SQLITE_MAX_EXPR_DEPTH
cargo:rerun-if-env-changed=SQLITE_MAX_COLUMN
cargo:rerun-if-env-changed=LIBSQLITE3_FLAGS
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(F:\opcode\src-tauri\target\debug\deps;F:\opcode\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;F:\opcode\node_modules\.bin;F:\opcode\node_modules\.bin;F:\node_modules\.bin;C:\mingw64\bin;C:\msys64\mingw64\bin;c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Pr;C:\go\go1.23.9\bin;C:\Program Files (x86)\Incredibuild;C:\Program Files\CMake\bin;C:\cloudtools;C:\Users\<USER>\.local\bin;C:\java\apache-maven-3.9.9\bin;C:\Program Files\Void\bin;;C:\Program Files (x86)\glab;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;C:\Users\<USER>\.cargo\bin;C:\Program Files\Google\Chrome\Application;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\msys64\mingw64\bin;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Pr;C:\Go\go1.21.13\bin;C:\Program Files;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\msys64\usr\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
sqlite3.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64
cargo:rustc-link-lib=static=sqlite3
cargo:rustc-link-search=native=F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out
cargo:lib_dir=F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out
