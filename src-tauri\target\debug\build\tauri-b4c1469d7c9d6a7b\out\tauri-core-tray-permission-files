["\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\tray\\autogenerated\\default.toml"]