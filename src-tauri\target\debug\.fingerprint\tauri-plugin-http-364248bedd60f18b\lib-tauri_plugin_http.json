{"rustc": 10895048813736897673, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 9651506899630869650, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9308551416210808831], [3150220818285335163, "url", false, 5315807009586788239], [8218178811151724123, "reqwest", false, 3800545513965059447], [8298091525883606470, "cookie_store", false, 1627654258585508751], [9010263965687315507, "http", false, 13448014166349877878], [9451456094439810778, "regex", false, 14025585605314257456], [9538054652646069845, "tokio", false, 8470097178834932105], [9689903380558560274, "serde", false, 7302387635316781145], [10755362358622467486, "tauri", false, 6619441761861462623], [10806645703491011684, "thiserror", false, 10121527808543703638], [13890802266741835355, "tauri_plugin_fs", false, 14538548428963896752], [15367738274754116744, "serde_json", false, 15129295461326157342], [15441187897486245138, "build_script_build", false, 1264129575348000736], [16066129441945555748, "bytes", false, 7203119217306711373], [17047088963840213854, "data_url", false, 1018655911515451090]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-364248bedd60f18b\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}