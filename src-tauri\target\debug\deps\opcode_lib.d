F:\opcode\src-tauri\target\debug\deps\libopcode_lib.rlib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs F:\opcode\src-tauri\target\debug\build\opcode-107e84bd59ccca52\out/d89adcc481aee032288cb7aae8ef24fd5e2f3e8ed1b3c96a5f00c3b02a824cd2

F:\opcode\src-tauri\target\debug\deps\opcode_lib.dll: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs F:\opcode\src-tauri\target\debug\build\opcode-107e84bd59ccca52\out/d89adcc481aee032288cb7aae8ef24fd5e2f3e8ed1b3c96a5f00c3b02a824cd2

F:\opcode\src-tauri\target\debug\deps\opcode_lib.lib: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs F:\opcode\src-tauri\target\debug\build\opcode-107e84bd59ccca52\out/d89adcc481aee032288cb7aae8ef24fd5e2f3e8ed1b3c96a5f00c3b02a824cd2

F:\opcode\src-tauri\target\debug\deps\opcode_lib.d: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\usage.rs src\commands\storage.rs src\commands\slash_commands.rs src\commands\proxy.rs src\process\mod.rs src\process\registry.rs F:\opcode\src-tauri\target\debug\build\opcode-107e84bd59ccca52\out/d89adcc481aee032288cb7aae8ef24fd5e2f3e8ed1b3c96a5f00c3b02a824cd2

src\lib.rs:
src\checkpoint\mod.rs:
src\checkpoint\manager.rs:
src\checkpoint\state.rs:
src\checkpoint\storage.rs:
src\claude_binary.rs:
src\commands\mod.rs:
src\commands\agents.rs:
src\commands\claude.rs:
src\commands\mcp.rs:
src\commands\usage.rs:
src\commands\storage.rs:
src\commands\slash_commands.rs:
src\commands\proxy.rs:
src\process\mod.rs:
src\process\registry.rs:
F:\opcode\src-tauri\target\debug\build\opcode-107e84bd59ccca52\out/d89adcc481aee032288cb7aae8ef24fd5e2f3e8ed1b3c96a5f00c3b02a824cd2:

# env-dep:CARGO_PKG_AUTHORS=mufeedvh:123vviekr
# env-dep:CARGO_PKG_DESCRIPTION=GUI app and Toolkit for Claude Code
# env-dep:CARGO_PKG_NAME=opcode
# env-dep:OUT_DIR=F:\\opcode\\src-tauri\\target\\debug\\build\\opcode-107e84bd59ccca52\\out
