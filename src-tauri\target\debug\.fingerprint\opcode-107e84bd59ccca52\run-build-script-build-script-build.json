{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11632912302601910407, "build_script_build", false, 7380222279335221744], [10755362358622467486, "build_script_build", false, 16087404247937073963], [13919194856117907555, "build_script_build", false, 6281646879595127954], [3834743577069889284, "build_script_build", false, 15581460762084892639], [13890802266741835355, "build_script_build", false, 14371299959703406105], [246920333930397414, "build_script_build", false, 214353211675156057], [15441187897486245138, "build_script_build", false, 1264129575348000736], [7849236192756901113, "build_script_build", false, 12168525280328777240], [17962022290347926134, "build_script_build", false, 1585376116264712540], [1582828171158827377, "build_script_build", false, 1152765876567083898], [18440762029541581206, "build_script_build", false, 7443814281728449258]], "local": [{"RerunIfChanged": {"output": "debug\\build\\opcode-107e84bd59ccca52\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}