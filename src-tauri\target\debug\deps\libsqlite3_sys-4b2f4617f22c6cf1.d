F:\opcode\src-tauri\target\debug\deps\liblibsqlite3_sys-4b2f4617f22c6cf1.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\error.rs F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out/bindgen.rs

F:\opcode\src-tauri\target\debug\deps\liblibsqlite3_sys-4b2f4617f22c6cf1.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\error.rs F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out/bindgen.rs

F:\opcode\src-tauri\target\debug\deps\libsqlite3_sys-4b2f4617f22c6cf1.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\error.rs F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out/bindgen.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\src\error.rs:
F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-31ec728388c429d2\out/bindgen.rs:

# env-dep:OUT_DIR=F:\\opcode\\src-tauri\\target\\debug\\build\\libsqlite3-sys-31ec728388c429d2\\out
