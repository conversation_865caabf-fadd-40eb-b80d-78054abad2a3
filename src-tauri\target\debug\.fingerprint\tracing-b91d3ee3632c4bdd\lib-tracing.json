{"rustc": 10895048813736897673, "features": "[\"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6355579909791343455, "path": 9279046595220919599, "deps": [[1906322745568073236, "pin_project_lite", false, 10047375642072349995], [3424551429995674438, "tracing_core", false, 10478192313197844259]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-b91d3ee3632c4bdd\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}