{"rustc": 10895048813736897673, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 3702988188742708284, "deps": [[778154619793643451, "hyper_util", false, 14360217213404614768], [784494742817713399, "tower_service", false, 3676445669032250325], [2883436298747778685, "pki_types", false, 16287195338963885596], [5907992341687085091, "webpki_roots", false, 13689174745413962576], [9010263965687315507, "http", false, 13448014166349877878], [9538054652646069845, "tokio", false, 8470097178834932105], [11895591994124935963, "tokio_rustls", false, 4877258225272971882], [11957360342995674422, "hyper", false, 10269217736095778695], [16400140949089969347, "rustls", false, 13468820166073032813]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-1dd3aa87815bfb37\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}