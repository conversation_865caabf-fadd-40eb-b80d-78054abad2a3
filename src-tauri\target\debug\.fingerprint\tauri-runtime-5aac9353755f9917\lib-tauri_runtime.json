{"rustc": 10895048813736897673, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 14544674386776249274, "deps": [[442785307232013896, "build_script_build", false, 11388853629559212677], [3150220818285335163, "url", false, 5315807009586788239], [4143744114649553716, "raw_window_handle", false, 16024919952200132162], [7606335748176206944, "dpi", false, 621635941899854351], [9010263965687315507, "http", false, 13448014166349877878], [9689903380558560274, "serde", false, 7302387635316781145], [10806645703491011684, "thiserror", false, 10121527808543703638], [11050281405049894993, "tauri_utils", false, 2194620399451770278], [13116089016666501665, "windows", false, 2366152367803156358], [15367738274754116744, "serde_json", false, 15129295461326157342], [16727543399706004146, "cookie", false, 16688528520764392486]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-5aac9353755f9917\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}