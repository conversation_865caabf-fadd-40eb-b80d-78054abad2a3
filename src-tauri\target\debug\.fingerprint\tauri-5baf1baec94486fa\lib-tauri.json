{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"http-range\", \"image\", \"image-png\", \"macos-private-api\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 6875419212244563512, "deps": [[40386456601120721, "percent_encoding", false, 7602432382075695993], [442785307232013896, "tauri_runtime", false, 13760426184531328432], [1200537532907108615, "url<PERSON><PERSON>n", false, 9308551416210808831], [3150220818285335163, "url", false, 5315807009586788239], [4143744114649553716, "raw_window_handle", false, 16024919952200132162], [4341921533227644514, "muda", false, 13920525550437804342], [4919829919303820331, "serialize_to_javascript", false, 12495704172771768974], [5986029879202738730, "log", false, 9991327803342368730], [7752760652095876438, "tauri_runtime_wry", false, 3100782798773811571], [8351317599104215083, "tray_icon", false, 2800603437813509193], [8539587424388551196, "webview2_com", false, 6734333369857695169], [8866577183823226611, "http_range", false, 13480962078150979962], [9010263965687315507, "http", false, 13448014166349877878], [9228235415475680086, "tauri_macros", false, 7483975918048881845], [9538054652646069845, "tokio", false, 8470097178834932105], [9689903380558560274, "serde", false, 7302387635316781145], [9920160576179037441, "getrandom", false, 16180408623467237269], [10229185211513642314, "mime", false, 16506038432130329962], [10629569228670356391, "futures_util", false, 14161969114160412234], [10755362358622467486, "build_script_build", false, 16087404247937073963], [10806645703491011684, "thiserror", false, 10121527808543703638], [11050281405049894993, "tauri_utils", false, 2194620399451770278], [11989259058781683633, "dunce", false, 15701999524367668848], [12565293087094287914, "window_vibrancy", false, 5545166453283101608], [12986574360607194341, "serde_repr", false, 6104625066621252331], [13028763805764736075, "image", false, 16976824469281932595], [13077543566650298139, "heck", false, 5922850431444217068], [13116089016666501665, "windows", false, 2366152367803156358], [13625485746686963219, "anyhow", false, 15680667910412842510], [15367738274754116744, "serde_json", false, 15129295461326157342], [16928111194414003569, "dirs", false, 1281165038842773590], [17155886227862585100, "glob", false, 6857009783500801805]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-5baf1baec94486fa\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}