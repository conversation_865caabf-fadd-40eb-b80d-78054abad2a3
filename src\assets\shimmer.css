/**
 * Shimmer animation styles
 * Provides a sword-like shimmer effect for elements
 */

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  40% {
    transform: translateX(100%);
    opacity: 0;
  }
  50% {
    transform: translateX(-100%);
    opacity: 0;
  }
  70% {
    opacity: 1;
  }
  90% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes shimmer-text {
  0% {
    background-position: -200% center;
  }
  45% {
    background-position: 200% center;
  }
  50% {
    background-position: -200% center;
  }
  95% {
    background-position: 200% center;
  }
  96%, 100% {
    background-position: 200% center;
    -webkit-text-fill-color: currentColor;
    background: none;
  }
}

/* Overlay variant: keeps the overlay text transparent and simply fades it out */
@keyframes shimmer-overlay {
  0% {
    background-position: -150% center;
    opacity: 1;
  }
  100% {
    background-position: 150% center;
    opacity: 0;
  }
}

@keyframes symbol-rotate {
  0% {
    content: '◐';
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  25% {
    content: '◓';
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    content: '◑';
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  75% {
    content: '◒';
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  100% {
    content: '◐';
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.shimmer-once {
  position: relative;
  display: inline-block;
  background: linear-gradient(
    105deg,
    currentColor 0%,
    currentColor 40%,
    #d97757 50%,
    currentColor 60%,
    currentColor 100%
  );
  background-size: 200% auto;
  background-position: -200% center;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: shimmer-text 1s ease-out forwards;
}

/* Ensures text remains visible after shimmer completes on engines
   where -webkit-text-fill-color set in keyframes may not persist */
.shimmer-fallback-visible {
  -webkit-text-fill-color: currentColor !important;
  background: none !important;
}

/* Layered brand text: base solid text plus shimmering overlay to avoid flicker */
.brand-text { position: relative; display: inline-block; }
.brand-text-solid { position: relative; color: currentColor; }
.brand-text-shimmer {
  position: absolute;
  inset: 0;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(
    90deg,
    transparent 0%,
    transparent 47%,
    rgba(217, 119, 87, 0.35) 50%,
    transparent 53%,
    transparent 100%
  );
  background-size: 300% auto;
  background-position: -150% center;
  -webkit-background-clip: text;
  background-clip: text;
  pointer-events: none;
  will-change: background-position, opacity;
  animation: shimmer-overlay 1.1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rotating-symbol {
  display: inline-block;
  color: #8B5CF6;
  font-size: 1.5rem; /* Make it bigger! */
  margin-right: 0.5rem;
  font-weight: bold;
  vertical-align: middle;
  position: relative;
  line-height: 1;
  top: -2px;
}

.rotating-symbol::before {
  content: '◐';
  display: inline-block;
  animation: symbol-rotate 2s linear infinite;
  font-size: inherit;
  line-height: inherit;
  vertical-align: baseline;
}

/* Allow pausing the rotating symbol via an extra class */
.rotating-symbol.paused::before {
  animation: none !important;
}

.shimmer-hover {
  position: relative;
  overflow: hidden;
}

.shimmer-hover::before {
  content: '';
  position: absolute;
  top: -50%;
  left: 0;
  width: 100%;
  height: 200%;
  background: linear-gradient(
    105deg,
    transparent 0%,
    transparent 40%,
    rgba(217, 119, 87, 0.4) 50%,
    transparent 60%,
    transparent 100%
  );
  transform: translateX(-100%) rotate(-10deg);
  opacity: 0;
  pointer-events: none;
  z-index: 1;
}

.shimmer-hover > * {
  position: relative;
  z-index: 2;
}

.shimmer-hover:hover::before {
  animation: shimmer 1s ease-out;
} 
