{"rustc": 10895048813736897673, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 15436716961641743716, "deps": [[376837177317575824, "softbuffer", false, 5372798282571583740], [442785307232013896, "tauri_runtime", false, 13760426184531328432], [3150220818285335163, "url", false, 5315807009586788239], [3722963349756955755, "once_cell", false, 8019171174034528550], [4143744114649553716, "raw_window_handle", false, 16024919952200132162], [5986029879202738730, "log", false, 9991327803342368730], [7752760652095876438, "build_script_build", false, 1685885246989570074], [8539587424388551196, "webview2_com", false, 6734333369857695169], [9010263965687315507, "http", false, 13448014166349877878], [11050281405049894993, "tauri_utils", false, 2194620399451770278], [13116089016666501665, "windows", false, 2366152367803156358], [13223659721939363523, "tao", false, 18138226462734329754], [14794439852947137341, "wry", false, 1468822878483429916]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-17183fa3c70c4524\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}