{"rustc": 10895048813736897673, "features": "[\"default\", \"public_suffix\", \"serde\", \"serde_json\"]", "declared_features": "[\"default\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"serde\", \"serde_json\", \"serde_ron\", \"wasm-bindgen\"]", "target": 8140962409157740669, "profile": 15657897354478470176, "path": 10669353915065964882, "deps": [[505596520502798227, "publicsuffix", false, 3124297266100769736], [3150220818285335163, "url", false, 5315807009586788239], [5986029879202738730, "log", false, 9991327803342368730], [6376232718484714452, "idna", false, 6509693946027364074], [9689903380558560274, "serde", false, 7302387635316781145], [11763018104473073732, "document_features", false, 2485989155894477065], [12409575957772518135, "time", false, 5683675688026874954], [15367738274754116744, "serde_json", false, 15129295461326157342], [16257276029081467297, "serde_derive", false, 8868307435655350701], [16727543399706004146, "cookie", false, 16688528520764392486]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie_store-0209acdbf66531f2\\dep-lib-cookie_store", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}