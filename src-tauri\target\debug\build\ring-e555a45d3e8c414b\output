cargo:rerun-if-env-changed=CARGO_MANIFEST_DIR
cargo:rerun-if-env-changed=CARGO_PKG_NAME
cargo:rerun-if-env-changed=CARGO_PKG_VERSION_MAJOR
cargo:rerun-if-env-changed=CARGO_PKG_VERSION_MINOR
cargo:rerun-if-env-changed=CARGO_PKG_VERSION_PATCH
cargo:rerun-if-env-changed=CARGO_PKG_VERSION_PRE
cargo:rerun-if-env-changed=CARGO_MANIFEST_LINKS
cargo:rerun-if-env-changed=RING_PREGENERATE_ASM
cargo:rerun-if-env-changed=OUT_DIR
cargo:rerun-if-env-changed=CARGO_CFG_TARGET_ARCH
cargo:rerun-if-env-changed=CARGO_CFG_TARGET_OS
cargo:rerun-if-env-changed=CARGO_CFG_TARGET_ENV
cargo:rerun-if-env-changed=CARGO_CFG_TARGET_ENDIAN
OPT_LEVEL = Some(0)
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\ring-e555a45d3e8c414b\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(F:\opcode\src-tauri\target\debug\deps;F:\opcode\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;F:\opcode\node_modules\.bin;F:\opcode\node_modules\.bin;F:\node_modules\.bin;C:\mingw64\bin;C:\msys64\mingw64\bin;c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Pr;C:\go\go1.23.9\bin;C:\Program Files (x86)\Incredibuild;C:\Program Files\CMake\bin;C:\cloudtools;C:\Users\<USER>\.local\bin;C:\java\apache-maven-3.9.9\bin;C:\Program Files\Void\bin;;C:\Program Files (x86)\glab;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;C:\Users\<USER>\.cargo\bin;C:\Program Files\Google\Chrome\Application;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\msys64\mingw64\bin;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Pr;C:\Go\go1.21.13\bin;C:\Program Files;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\msys64\usr\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
curve25519.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\ring-0.17.14\crypto\curve25519\./curve25519_tables.h(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\ring-0.17.14\crypto\curve25519\../../third_party/fiat/curve25519_64_msvc.h(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
aes_nohw.c
montgomery.c
montgomery_inv.c
ecp_nistz.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\ring-0.17.14\crypto\fipsmodule\ec\ecp_nistz.h(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
gfp_p256.c
gfp_p384.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\ring-0.17.14\crypto\fipsmodule\ec\ecp_nistz.h(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
p256.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\ring-0.17.14\crypto\fipsmodule\ec\./util.h(1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
limbs.c
mem.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
poly1305.c
crypto.c
cpu_intel.c
curve25519_64_adx.c
p256-nistz.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rustc-link-lib=static=ring_core_0_17_14_
OPT_LEVEL = Some(0)
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\ring-e555a45d3e8c414b\out)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(F:\opcode\src-tauri\target\debug\deps;F:\opcode\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;F:\opcode\node_modules\.bin;F:\opcode\node_modules\.bin;F:\node_modules\.bin;C:\mingw64\bin;C:\msys64\mingw64\bin;c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Pr;C:\go\go1.23.9\bin;C:\Program Files (x86)\Incredibuild;C:\Program Files\CMake\bin;C:\cloudtools;C:\Users\<USER>\.local\bin;C:\java\apache-maven-3.9.9\bin;C:\Program Files\Void\bin;;C:\Program Files (x86)\glab;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;C:\Users\<USER>\.cargo\bin;C:\Program Files\Google\Chrome\Application;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\msys64\mingw64\bin;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Pr;C:\Go\go1.21.13\bin;C:\Program Files;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\msys64\usr\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
constant_time_test.c
cargo:rustc-link-lib=static=ring_core_0_17_14__test
cargo:rustc-link-search=native=F:\opcode\src-tauri\target\debug\build\ring-e555a45d3e8c414b\out
cargo:rerun-if-changed=crypto\chacha\asm\chacha-armv4.pl
cargo:rerun-if-changed=crypto\chacha\asm\chacha-armv8.pl
cargo:rerun-if-changed=crypto\chacha\asm\chacha-x86.pl
cargo:rerun-if-changed=crypto\chacha\asm\chacha-x86_64.pl
cargo:rerun-if-changed=crypto\cipher\asm\chacha20_poly1305_armv8.pl
cargo:rerun-if-changed=crypto\cipher\asm\chacha20_poly1305_x86_64.pl
cargo:rerun-if-changed=crypto\constant_time_test.c
cargo:rerun-if-changed=crypto\cpu_intel.c
cargo:rerun-if-changed=crypto\crypto.c
cargo:rerun-if-changed=crypto\curve25519\asm\x25519-asm-arm.S
cargo:rerun-if-changed=crypto\curve25519\curve25519.c
cargo:rerun-if-changed=crypto\curve25519\curve25519_64_adx.c
cargo:rerun-if-changed=crypto\curve25519\curve25519_tables.h
cargo:rerun-if-changed=crypto\curve25519\internal.h
cargo:rerun-if-changed=crypto\fipsmodule\aes\aes_nohw.c
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\aes-gcm-avx2-x86_64.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\aesni-gcm-x86_64.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\aesni-x86.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\aesni-x86_64.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\aesv8-armx.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\aesv8-gcm-armv8.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\bsaes-armv7.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\ghash-armv4.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\ghash-neon-armv8.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\ghash-x86.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\ghash-x86_64.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\ghashv8-armx.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\vpaes-armv7.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\vpaes-armv8.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\vpaes-x86.pl
cargo:rerun-if-changed=crypto\fipsmodule\aes\asm\vpaes-x86_64.pl
cargo:rerun-if-changed=crypto\fipsmodule\bn\asm\armv4-mont.pl
cargo:rerun-if-changed=crypto\fipsmodule\bn\asm\armv8-mont.pl
cargo:rerun-if-changed=crypto\fipsmodule\bn\asm\x86-mont.pl
cargo:rerun-if-changed=crypto\fipsmodule\bn\asm\x86_64-mont.pl
cargo:rerun-if-changed=crypto\fipsmodule\bn\asm\x86_64-mont5.pl
cargo:rerun-if-changed=crypto\fipsmodule\bn\internal.h
cargo:rerun-if-changed=crypto\fipsmodule\bn\montgomery.c
cargo:rerun-if-changed=crypto\fipsmodule\bn\montgomery_inv.c
cargo:rerun-if-changed=crypto\fipsmodule\ec\asm\p256-armv8-asm.pl
cargo:rerun-if-changed=crypto\fipsmodule\ec\asm\p256-x86_64-asm.pl
cargo:rerun-if-changed=crypto\fipsmodule\ec\ecp_nistz.c
cargo:rerun-if-changed=crypto\fipsmodule\ec\ecp_nistz.h
cargo:rerun-if-changed=crypto\fipsmodule\ec\ecp_nistz384.h
cargo:rerun-if-changed=crypto\fipsmodule\ec\ecp_nistz384.inl
cargo:rerun-if-changed=crypto\fipsmodule\ec\gfp_p256.c
cargo:rerun-if-changed=crypto\fipsmodule\ec\gfp_p384.c
cargo:rerun-if-changed=crypto\fipsmodule\ec\p256-nistz-table.h
cargo:rerun-if-changed=crypto\fipsmodule\ec\p256-nistz.c
cargo:rerun-if-changed=crypto\fipsmodule\ec\p256-nistz.h
cargo:rerun-if-changed=crypto\fipsmodule\ec\p256.c
cargo:rerun-if-changed=crypto\fipsmodule\ec\p256_shared.h
cargo:rerun-if-changed=crypto\fipsmodule\ec\p256_table.h
cargo:rerun-if-changed=crypto\fipsmodule\ec\util.h
cargo:rerun-if-changed=crypto\fipsmodule\sha\asm\sha256-armv4.pl
cargo:rerun-if-changed=crypto\fipsmodule\sha\asm\sha512-armv4.pl
cargo:rerun-if-changed=crypto\fipsmodule\sha\asm\sha512-armv8.pl
cargo:rerun-if-changed=crypto\fipsmodule\sha\asm\sha512-x86_64.pl
cargo:rerun-if-changed=crypto\internal.h
cargo:rerun-if-changed=crypto\limbs\limbs.c
cargo:rerun-if-changed=crypto\limbs\limbs.h
cargo:rerun-if-changed=crypto\limbs\limbs.inl
cargo:rerun-if-changed=crypto\mem.c
cargo:rerun-if-changed=crypto\perlasm\arm-xlate.pl
cargo:rerun-if-changed=crypto\perlasm\x86asm.pl
cargo:rerun-if-changed=crypto\perlasm\x86gas.pl
cargo:rerun-if-changed=crypto\perlasm\x86nasm.pl
cargo:rerun-if-changed=crypto\perlasm\x86_64-xlate.pl
cargo:rerun-if-changed=crypto\poly1305\poly1305.c
cargo:rerun-if-changed=crypto\poly1305\poly1305_arm.c
cargo:rerun-if-changed=crypto\poly1305\poly1305_arm_asm.S
cargo:rerun-if-changed=include\ring-core\aes.h
cargo:rerun-if-changed=include\ring-core\asm_base.h
cargo:rerun-if-changed=include\ring-core\base.h
cargo:rerun-if-changed=include\ring-core\check.h
cargo:rerun-if-changed=include\ring-core\mem.h
cargo:rerun-if-changed=include\ring-core\target.h
cargo:rerun-if-changed=include\ring-core\type_check.h
cargo:rerun-if-changed=third_party/fiat\asm\fiat_curve25519_adx_mul.S
cargo:rerun-if-changed=third_party/fiat\asm\fiat_curve25519_adx_square.S
cargo:rerun-if-changed=third_party/fiat\curve25519_32.h
cargo:rerun-if-changed=third_party/fiat\curve25519_64.h
cargo:rerun-if-changed=third_party/fiat\curve25519_64_adx.h
cargo:rerun-if-changed=third_party/fiat\curve25519_64_msvc.h
cargo:rerun-if-changed=third_party/fiat\LICENSE
cargo:rerun-if-changed=third_party/fiat\p256_32.h
cargo:rerun-if-changed=third_party/fiat\p256_64.h
cargo:rerun-if-changed=third_party/fiat\p256_64_msvc.h
