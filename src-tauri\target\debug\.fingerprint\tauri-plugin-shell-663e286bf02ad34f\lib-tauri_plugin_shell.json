{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 11960066329651246842, "deps": [[500211409582349667, "shared_child", false, 579330756058131895], [1582828171158827377, "build_script_build", false, 1152765876567083898], [5986029879202738730, "log", false, 9991327803342368730], [9451456094439810778, "regex", false, 14025585605314257456], [9538054652646069845, "tokio", false, 8470097178834932105], [9689903380558560274, "serde", false, 7302387635316781145], [10755362358622467486, "tauri", false, 6619441761861462623], [10806645703491011684, "thiserror", false, 10121527808543703638], [11337703028400419576, "os_pipe", false, 4280935934242794402], [14564311161534545801, "encoding_rs", false, 12746824173510703109], [15367738274754116744, "serde_json", false, 15129295461326157342], [16192041687293812804, "open", false, 16695723100363769875]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-663e286bf02ad34f\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}