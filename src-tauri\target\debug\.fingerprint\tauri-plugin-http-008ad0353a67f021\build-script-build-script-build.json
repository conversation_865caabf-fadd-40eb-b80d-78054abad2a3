{"rustc": 10895048813736897673, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 9010888395901654593, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 16164685342997030339], [2326493920556799156, "tauri_plugin", false, 1454794682172187272], [3150220818285335163, "url", false, 3756853273449794540], [6913375703034175521, "schemars", false, 1918259654783669367], [9451456094439810778, "regex", false, 14025585605314257456], [9689903380558560274, "serde", false, 11800481973374590501]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-008ad0353a67f021\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}