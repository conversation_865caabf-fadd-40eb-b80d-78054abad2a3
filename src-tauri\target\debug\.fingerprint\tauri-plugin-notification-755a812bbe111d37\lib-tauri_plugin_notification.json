{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 15657897354478470176, "path": 388104217758051395, "deps": [[947818755262499932, "notify_rust", false, 10103053485052538562], [3150220818285335163, "url", false, 5315807009586788239], [5986029879202738730, "log", false, 9991327803342368730], [7849236192756901113, "build_script_build", false, 12168525280328777240], [9689903380558560274, "serde", false, 7302387635316781145], [10755362358622467486, "tauri", false, 6619441761861462623], [10806645703491011684, "thiserror", false, 10121527808543703638], [12409575957772518135, "time", false, 5683675688026874954], [12986574360607194341, "serde_repr", false, 6104625066621252331], [13208667028893622512, "rand", false, 13323550744404333321], [15367738274754116744, "serde_json", false, 15129295461326157342]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-755a812bbe111d37\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}