<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
  <key>NSRequiresAquaSystemAppearance</key>
  <false/>
  <key>LSMinimumSystemVersion</key>
  <string>10.15</string>
  <key>CFBundleShortVersionString</key>
  <string>0.2.1</string>
  <key>CFBundleName</key>
  <string>opcode</string>
  <key>CFBundleDisplayName</key>
  <string>opcode</string>
  <key>CFBundleIdentifier</key>
  <string>opcode.asterisk.so</string>
  <key>CFBundleDocumentTypes</key>
  <array>
    <dict>
      <key>CFBundleTypeName</key>
      <string>opcode Agent</string>
      <key>CFBundleTypeRole</key>
      <string>Editor</string>
      <key>CFBundleTypeExtensions</key>
      <array>
        <string>opcode.json</string>
      </array>
      <key>CFBundleTypeIconFile</key>
      <string>icon.icns</string>
      <key>LSHandlerRank</key>
      <string>Owner</string>
    </dict>
  </array>
  <key>NSAppleEventsUsageDescription</key>
  <string>opcode needs to send Apple Events to other applications.</string>
  <key>NSAppleScriptEnabled</key>
  <true/>
  <key>NSCameraUsageDescription</key>
  <string>opcode needs camera access for capturing images for AI processing.</string>
  <key>NSMicrophoneUsageDescription</key>
  <string>opcode needs microphone access for voice input features.</string>
</dict>
</plist>
