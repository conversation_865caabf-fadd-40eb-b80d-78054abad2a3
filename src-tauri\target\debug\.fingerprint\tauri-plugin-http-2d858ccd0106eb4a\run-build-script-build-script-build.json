{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 16087404247937073963], [13890802266741835355, "build_script_build", false, 14371299959703406105], [15441187897486245138, "build_script_build", false, 13497207784764595438]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-2d858ccd0106eb4a\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}