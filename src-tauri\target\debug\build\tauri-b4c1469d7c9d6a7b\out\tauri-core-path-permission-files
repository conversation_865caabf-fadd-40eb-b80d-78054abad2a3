["\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\path\\autogenerated\\default.toml"]