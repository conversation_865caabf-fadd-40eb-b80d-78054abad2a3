{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 13884414492577201392, "deps": [[1462335029370885857, "quick_xml", false, 10291994764912817162], [3334271191048661305, "windows_version", false, 15084331408714622677], [10806645703491011684, "thiserror", false, 10121527808543703638], [13116089016666501665, "windows", false, 2366152367803156358]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-5b622199f64c7a85\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}