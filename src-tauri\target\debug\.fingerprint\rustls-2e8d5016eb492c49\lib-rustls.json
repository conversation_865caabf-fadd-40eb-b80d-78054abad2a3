{"rustc": 10895048813736897673, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 16174577419115896063, "deps": [[2883436298747778685, "pki_types", false, 16287195338963885596], [3722963349756955755, "once_cell", false, 8019171174034528550], [5491919304041016563, "ring", false, 4983921413349342264], [6528079939221783635, "zeroize", false, 12545292436346467415], [16400140949089969347, "build_script_build", false, 16404900576224077939], [17003143334332120809, "subtle", false, 4106543407445729243], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 874493927510834591]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-2e8d5016eb492c49\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}