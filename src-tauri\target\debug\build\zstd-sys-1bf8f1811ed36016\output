cargo:rerun-if-env-changed=ZSTD_SYS_USE_PKG_CONFIG
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\zstd-sys-1bf8f1811ed36016\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(F:\opcode\src-tauri\target\debug\deps;F:\opcode\src-tauri\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;F:\opcode\node_modules\.bin;F:\opcode\node_modules\.bin;F:\node_modules\.bin;C:\mingw64\bin;C:\msys64\mingw64\bin;c:\Users\<USER>\AppData\Local\Programs\Cursor\resources\app\bin;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Pr;C:\go\go1.23.9\bin;C:\Program Files (x86)\Incredibuild;C:\Program Files\CMake\bin;C:\cloudtools;C:\Users\<USER>\.local\bin;C:\java\apache-maven-3.9.9\bin;C:\Program Files\Void\bin;;C:\Program Files (x86)\glab;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;C:\Users\<USER>\.cargo\bin;C:\Program Files\Google\Chrome\Application;C:\Program Files\VanDyke Software\Clients\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\TortoiseSVN\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\gsudo\;C:\java\jdk-*********\bin;C:\airtest_adb;C:\msys64\mingw64\bin;C:\Program Files\GitHub CLI\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\LocalCache\local-packages\Python310\Scripts\;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Roaming\npm;C:\Pr;C:\Go\go1.21.13\bin;C:\Program Files;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\msys64\usr\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\zstd-sys-1bf8f1811ed36016\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\zstd-sys-1bf8f1811ed36016\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
OUT_DIR = Some(F:\opcode\src-tauri\target\debug\build\zstd-sys-1bf8f1811ed36016\out)
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
TARGET = Some(x86_64-pc-windows-msvc)
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
debug.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
entropy_common.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
error_private.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
fse_decompress.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
pool.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
threading.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_common.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
fse_compress.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
hist.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
huf_compress.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstd_compress.c
exit code: cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_compress_literals.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_compress_sequences.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_compress_superblock.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_double_fast.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_fast.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_lazy.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_ldm.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                                                                                                  
zstd_opt.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_preSplit.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstdmt_compress.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
huf_decompress.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_ddict.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_decompress.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_decompress_block.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
cover.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
divsufsort.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
fastcover.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zdict.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
zstd_v01.c
exit code: cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_v02.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_v03.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_v04.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_v05.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_v06.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
zstd_v07.c
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
cargo:warning=cl: 命令行 warning D9002 :忽略未知选项“-fvisibility=hidden”
                          
0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\atlmfc\lib\x64
cargo:rustc-link-lib=static=zstd
cargo:rustc-link-search=native=F:\opcode\src-tauri\target\debug\build\zstd-sys-1bf8f1811ed36016\out
cargo:root=F:\opcode\src-tauri\target\debug\build\zstd-sys-1bf8f1811ed36016\out
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\zstd-sys-2.0.15+zstd.1.5.7\zstd/lib
