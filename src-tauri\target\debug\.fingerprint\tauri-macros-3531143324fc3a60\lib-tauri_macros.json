{"rustc": 10895048813736897673, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 14133677171286637596, "deps": [[3060637413840920116, "proc_macro2", false, 13988116177612567140], [7341521034400937459, "tauri_codegen", false, 12001864247629097056], [11050281405049894993, "tauri_utils", false, 14523656501505049074], [13077543566650298139, "heck", false, 5922850431444217068], [17990358020177143287, "quote", false, 11663189590027116867], [18149961000318489080, "syn", false, 430207270993599230]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-3531143324fc3a60\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}