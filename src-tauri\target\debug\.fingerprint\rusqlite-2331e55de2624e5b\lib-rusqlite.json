{"rustc": 10895048813736897673, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 15657897354478470176, "path": 10368489017427459657, "deps": [[3056352129074654578, "hashlink", false, 14656404275776345365], [3666196340704888985, "smallvec", false, 15048312826293411749], [5510864063823219921, "fallible_streaming_iterator", false, 12224715660170406428], [7896293946984509699, "bitflags", false, 11039806081745277818], [12860549049674006569, "fallible_iterator", false, 3822011152421219287], [16675652872862304210, "libsqlite3_sys", false, 12065047497004897361]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rusqlite-2331e55de2624e5b\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}