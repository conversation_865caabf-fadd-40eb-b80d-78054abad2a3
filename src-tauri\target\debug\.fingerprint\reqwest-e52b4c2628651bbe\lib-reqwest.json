{"rustc": 10895048813736897673, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 11833470040658361283, "deps": [[40386456601120721, "percent_encoding", false, 7602432382075695993], [418947936956741439, "h2", false, 9819357760410756094], [778154619793643451, "hyper_util", false, 14360217213404614768], [784494742817713399, "tower_service", false, 3676445669032250325], [1288403060204016458, "tokio_util", false, 4667757788346769419], [1788832197870803419, "hyper_rustls", false, 5513307237667403434], [1906322745568073236, "pin_project_lite", false, 10047375642072349995], [2054153378684941554, "tower_http", false, 16521274506900408016], [2517136641825875337, "sync_wrapper", false, 4274207418579727799], [2883436298747778685, "rustls_pki_types", false, 16287195338963885596], [3150220818285335163, "url", false, 5315807009586788239], [5695049318159433696, "tower", false, 9366405263260460217], [5907992341687085091, "webpki_roots", false, 13689174745413962576], [5986029879202738730, "log", false, 9991327803342368730], [7620660491849607393, "futures_core", false, 1236560927279549109], [8298091525883606470, "cookie_store", false, 1627654258585508751], [9010263965687315507, "http", false, 13448014166349877878], [9538054652646069845, "tokio", false, 8470097178834932105], [9689903380558560274, "serde", false, 7302387635316781145], [10229185211513642314, "mime", false, 16506038432130329962], [10629569228670356391, "futures_util", false, 14161969114160412234], [11895591994124935963, "tokio_rustls", false, 4877258225272971882], [11957360342995674422, "hyper", false, 10269217736095778695], [12186126227181294540, "tokio_native_tls", false, 6187202165814818408], [13077212702700853852, "base64", false, 17347692872555988778], [14084095096285906100, "http_body", false, 4780749114932483082], [14564311161534545801, "encoding_rs", false, 12746824173510703109], [15367738274754116744, "serde_json", false, 15129295461326157342], [16066129441945555748, "bytes", false, 7203119217306711373], [16400140949089969347, "rustls", false, 13468820166073032813], [16542808166767769916, "serde_urlencoded", false, 8800487056207034962], [16727543399706004146, "cookie_crate", false, 16688528520764392486], [16785601910559813697, "native_tls_crate", false, 14649636834480358013], [16900715236047033623, "http_body_util", false, 12062982426827816882], [18273243456331255970, "hyper_tls", false, 12447789327585569930]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-e52b4c2628651bbe\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}