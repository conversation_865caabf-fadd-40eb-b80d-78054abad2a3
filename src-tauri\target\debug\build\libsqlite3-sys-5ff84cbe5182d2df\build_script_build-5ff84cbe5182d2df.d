F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-5ff84cbe5182d2df\build_script_build-5ff84cbe5182d2df.exe: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs

F:\opcode\src-tauri\target\debug\build\libsqlite3-sys-5ff84cbe5182d2df\build_script_build-5ff84cbe5182d2df.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.30.1\build.rs:

# env-dep:CARGO_MANIFEST_DIR=C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\libsqlite3-sys-0.30.1
