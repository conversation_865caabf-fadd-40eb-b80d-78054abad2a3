{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 15657897354478470176, "path": 6872955229028164599, "deps": [[1573238666360410412, "rand_chacha", false, 16816435575202136787], [18130209639506977569, "rand_core", false, 15407441357687821493]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-c10d4f3d28466d40\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}