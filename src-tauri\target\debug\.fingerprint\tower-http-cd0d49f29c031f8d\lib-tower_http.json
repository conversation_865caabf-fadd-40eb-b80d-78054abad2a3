{"rustc": 10895048813736897673, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 9087979588216027590, "deps": [[784494742817713399, "tower_service", false, 3676445669032250325], [1906322745568073236, "pin_project_lite", false, 10047375642072349995], [4121350475192885151, "iri_string", false, 6745638621019041497], [5695049318159433696, "tower", false, 9366405263260460217], [7712452662827335977, "tower_layer", false, 16792281725982619428], [7896293946984509699, "bitflags", false, 11039806081745277818], [9010263965687315507, "http", false, 13448014166349877878], [10629569228670356391, "futures_util", false, 14161969114160412234], [14084095096285906100, "http_body", false, 4780749114932483082], [16066129441945555748, "bytes", false, 7203119217306711373]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-cd0d49f29c031f8d\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}