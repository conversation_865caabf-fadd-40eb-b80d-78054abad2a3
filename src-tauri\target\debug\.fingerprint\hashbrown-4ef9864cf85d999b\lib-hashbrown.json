{"rustc": 10895048813736897673, "features": "[\"ahash\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 7819454923721926422, "deps": [[966925859616469517, "ahash", false, 3556332449364305014]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-4ef9864cf85d999b\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}