["\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\F:\\opcode\\src-tauri\\target\\debug\\build\\tauri-b4c1469d7c9d6a7b\\out\\permissions\\app\\autogenerated\\default.toml"]