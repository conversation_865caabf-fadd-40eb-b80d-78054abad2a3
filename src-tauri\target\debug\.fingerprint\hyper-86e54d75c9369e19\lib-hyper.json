{"rustc": 10895048813736897673, "features": "[\"client\", \"default\", \"http1\", \"http2\"]", "declared_features": "[\"capi\", \"client\", \"default\", \"ffi\", \"full\", \"http1\", \"http2\", \"nightly\", \"server\", \"tracing\"]", "target": 9574292076208557625, "profile": 10765686629543842738, "path": 9879331113562034650, "deps": [[418947936956741439, "h2", false, 9819357760410756094], [1569313478171189446, "want", false, 11046418139792890167], [1811549171721445101, "futures_channel", false, 191745645230397679], [1906322745568073236, "pin_project_lite", false, 10047375642072349995], [3666196340704888985, "smallvec", false, 15048312826293411749], [6163892036024256188, "httparse", false, 6501442189737356255], [7695812897323945497, "itoa", false, 5848073298646213696], [9010263965687315507, "http", false, 13448014166349877878], [9538054652646069845, "tokio", false, 8470097178834932105], [10629569228670356391, "futures_util", false, 14161969114160412234], [14084095096285906100, "http_body", false, 4780749114932483082], [16066129441945555748, "bytes", false, 7203119217306711373]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-86e54d75c9369e19\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}