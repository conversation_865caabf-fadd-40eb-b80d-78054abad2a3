cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=F:\opcode\src-tauri\target\debug\build\tauri-plugin-clipboard-manager-5231f1901f09fd2e\out\tauri-plugin-clipboard-manager-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-clipboard-manager-2.2.3\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
